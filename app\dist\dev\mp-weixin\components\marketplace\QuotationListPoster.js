"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const __unplugin_components_4 = () => "../l-painter/l-painter.js";
const __unplugin_components_3 = () => "../l-painter-qrcode/l-painter-qrcode.js";
const __unplugin_components_2 = () => "../l-painter-view/l-painter-view.js";
const __unplugin_components_1 = () => "../l-painter-text/l-painter-text.js";
const __unplugin_components_0 = () => "../l-painter-image/l-painter-image.js";
if (!Array) {
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_l_painter_image2 = __unplugin_components_0;
  const _easycom_l_painter_text2 = __unplugin_components_1;
  const _easycom_l_painter_view2 = __unplugin_components_2;
  const _easycom_l_painter_qrcode2 = __unplugin_components_3;
  const _easycom_l_painter2 = __unplugin_components_4;
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  (_easycom_wd_loading2 + _easycom_l_painter_image2 + _easycom_l_painter_text2 + _easycom_l_painter_view2 + _easycom_l_painter_qrcode2 + _easycom_l_painter2 + _easycom_wd_button2)();
}
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_l_painter_image = () => "../l-painter-image/l-painter-image.js";
const _easycom_l_painter_text = () => "../l-painter-text/l-painter-text.js";
const _easycom_l_painter_view = () => "../l-painter-view/l-painter-view.js";
const _easycom_l_painter_qrcode = () => "../l-painter-qrcode/l-painter-qrcode.js";
const _easycom_l_painter = () => "../l-painter/l-painter.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_loading + _easycom_l_painter_image + _easycom_l_painter_text + _easycom_l_painter_view + _easycom_l_painter_qrcode + _easycom_l_painter + _easycom_wd_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationListPoster"
}), {
  __name: "QuotationListPoster",
  props: {
    visible: { type: Boolean },
    userProfile: {},
    quotationList: {}
  },
  emits: ["update:visible", "success", "fail"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const isLoading = common_vendor.ref(false);
    const posterRef = common_vendor.ref();
    const generatedImagePath = common_vendor.ref("");
    const showPreview = common_vendor.ref(false);
    const previewImagePath = common_vendor.ref("");
    const isVisible = common_vendor.computed({
      get: () => props.visible,
      set: (value) => emit("update:visible", value)
    });
    const displayQuotationList = common_vendor.computed(() => {
      return props.quotationList.slice(0, 3);
    });
    const miniProgramPath = common_vendor.computed(() => {
      var _a;
      return `/pages/quotes/public-list?id=${((_a = props.userProfile) == null ? void 0 : _a.id) || ""}`;
    });
    common_vendor.watch(() => props.visible, (newVisible) => {
      if (newVisible && props.userProfile && props.quotationList.length > 0) {
        isLoading.value = true;
        setTimeout(() => {
          generatePoster();
        }, 100);
      }
    });
    function generatePoster() {
      return __async(this, null, function* () {
      });
    }
    function handleDone(result) {
      console.log("lime-painter 绘制完成:", result);
      if (posterRef.value) {
        posterRef.value.canvasToTempFilePathSync({
          fileType: "jpg",
          quality: 0.9,
          success: (res) => {
            console.log("海报生成成功:", res.tempFilePath);
            handleSuccess(res.tempFilePath);
          },
          fail: (error) => {
            console.error("海报生成失败:", error);
            handleFail(error);
          }
        });
      }
    }
    function handleSuccess(imagePath) {
      isLoading.value = false;
      generatedImagePath.value = imagePath;
      previewImagePath.value = imagePath;
      emit("success", imagePath);
      showPreview.value = true;
    }
    function handleFail(error) {
      isLoading.value = false;
      console.error("海报生成失败:", error);
      emit("fail", error);
      common_vendor.index.showToast({
        title: "海报生成失败",
        icon: "error"
      });
    }
    function checkPhotoAlbumPermission() {
      return __async(this, null, function* () {
        return new Promise((resolve) => {
          common_vendor.index.getSetting({
            success: (res) => {
              const authSetting = res.authSetting;
              if (authSetting["scope.writePhotosAlbum"] === void 0) {
                resolve(true);
              } else if (authSetting["scope.writePhotosAlbum"] === true) {
                resolve(true);
              } else {
                resolve(false);
              }
            },
            fail: () => {
              resolve(false);
            }
          });
        });
      });
    }
    function requestPhotoAlbumPermission() {
      return __async(this, null, function* () {
        return new Promise((resolve) => {
          common_vendor.index.authorize({
            scope: "scope.writePhotosAlbum",
            success: () => {
              resolve(true);
            },
            fail: () => {
              common_vendor.index.showModal({
                title: "需要相册权限",
                content: "保存图片需要访问您的相册，请在设置中开启相册权限",
                showCancel: true,
                confirmText: "去设置",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    common_vendor.index.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting["scope.writePhotosAlbum"]) {
                          resolve(true);
                        } else {
                          resolve(false);
                        }
                      },
                      fail: () => {
                        resolve(false);
                      }
                    });
                  } else {
                    resolve(false);
                  }
                }
              });
            }
          });
        });
      });
    }
    function saveToAlbum() {
      return __async(this, null, function* () {
        if (!previewImagePath.value) {
          common_vendor.index.showToast({
            title: "请先生成海报",
            icon: "none"
          });
          return;
        }
        try {
          const hasPermission = yield checkPhotoAlbumPermission();
          let canSave = hasPermission;
          if (!hasPermission) {
            canSave = yield requestPhotoAlbumPermission();
          }
          if (!canSave) {
            common_vendor.index.showToast({
              title: "需要相册权限才能保存",
              icon: "none"
            });
            return;
          }
          common_vendor.index.saveImageToPhotosAlbum({
            filePath: previewImagePath.value,
            success: () => {
              common_vendor.index.showToast({
                title: "保存成功",
                icon: "success"
              });
            },
            fail: (error) => {
              console.error("保存失败:", error);
              common_vendor.index.showToast({
                title: "保存失败",
                icon: "error"
              });
            }
          });
        } catch (error) {
          console.error("保存过程出错:", error);
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      });
    }
    function closePreview() {
      showPreview.value = false;
      emit("update:visible", false);
      generatedImagePath.value = "";
      previewImagePath.value = "";
    }
    function formatPrice(quotation) {
      if (quotation.priceType === "Fixed") {
        return formatLargeNumber(quotation.price);
      } else {
        const formatted = formatLargeNumber(Math.abs(quotation.price));
        return quotation.price >= 0 ? `+${formatted}` : `-${formatted}`;
      }
    }
    function formatLargeNumber(num) {
      const absNum = Math.abs(num);
      if (absNum < 1e4) {
        return absNum.toLocaleString();
      }
      if (absNum < 1e8) {
        const wan = absNum / 1e4;
        return wan >= 100 ? Math.round(wan) + "万" : wan.toFixed(1) + "万";
      }
      const yi = absNum / 1e8;
      return yi >= 100 ? Math.round(yi) + "亿" : yi.toFixed(1) + "亿";
    }
    function getContractName(quotation) {
      var _a;
      return ((_a = quotation.instrumentRef) == null ? void 0 : _a.instrument_id) || "未指定合约";
    }
    function getPriceStyle(quotation) {
      const baseStyle = "font-size: 32rpx; font-weight: 900; display: block;";
      if (quotation.priceType === "Fixed") {
        return `${baseStyle} color: #1890ff;`;
      } else {
        const color = quotation.price >= 0 ? "#ff4d4f" : "#52c41a";
        return `${baseStyle} color: ${color};`;
      }
    }
    function onImageLoad() {
      console.log("预览图片加载完成");
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isVisible.value
      }, isVisible.value ? common_vendor.e({
        b: isLoading.value
      }, isLoading.value ? {
        c: common_vendor.p({
          size: "48rpx"
        })
      } : {}, {
        d: common_vendor.p({
          src: _ctx.userProfile.headerImg || "/static/images/default-avatar.png",
          css: "width: 120rpx; height: 120rpx; border-radius: 50%; object-fit: cover; border: 4rpx solid rgba(102, 126, 234, 0.1); float: left;"
        }),
        e: common_vendor.p({
          text: _ctx.userProfile.companyName || "未指定公司名称",
          css: "font-size: 36rpx; font-weight: 600; color: #1a1a1a; line-height: 50rpx; display: block; margin-bottom: 16rpx;"
        }),
        f: common_vendor.p({
          text: _ctx.userProfile.nickName ? `联系人：${_ctx.userProfile.nickName}` : "",
          css: "font-size: 28rpx; color: #666; line-height: 40rpx; display: block; margin-bottom: 12rpx;"
        }),
        g: common_vendor.p({
          text: "手机：***-****-****",
          css: "font-size: 26rpx; color: #909399; line-height: 36rpx; display: block;"
        }),
        h: common_vendor.p({
          css: "margin-left: 150rpx；display: inline-block;"
        }),
        i: common_vendor.p({
          css: "background: rgba(255, 255, 255, 0.95); border-radius: 20rpx; padding: 40rpx; margin-bottom: 30rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);"
        }),
        j: common_vendor.f(displayQuotationList.value, (quotation, k0, i0) => {
          return {
            a: "ab939cb2-10-" + i0 + "," + ("ab939cb2-9-" + i0),
            b: common_vendor.p({
              text: quotation.title,
              css: "font-size: 28rpx; font-weight: 500; color: #303133; line-height: 1.4; display: block; margin-bottom: 12rpx;"
            }),
            c: "ab939cb2-13-" + i0 + "," + ("ab939cb2-12-" + i0),
            d: common_vendor.p({
              text: quotation.commodityName || "",
              css: "font-size: 26rpx; color: #606266; display: inline-block; background: #f0f9ff; padding: 4rpx 12rpx; border-radius: 8rpx; margin-right: 12rpx;"
            }),
            e: "ab939cb2-14-" + i0 + "," + ("ab939cb2-12-" + i0),
            f: common_vendor.p({
              text: quotation.deliveryLocation || "",
              css: "font-size: 26rpx; color: #606266; display: inline-block; background: #f0f9f4; padding: 4rpx 12rpx; border-radius: 8rpx;"
            }),
            g: "ab939cb2-12-" + i0 + "," + ("ab939cb2-11-" + i0),
            h: "ab939cb2-16-" + i0 + "," + ("ab939cb2-15-" + i0),
            i: common_vendor.p({
              text: quotation.priceType === "Basis" ? getContractName(quotation) : "",
              css: "font-size: 24rpx; color: #595959; font-weight: 700; letter-spacing: 1rpx; display: block; margin-bottom: 4rpx;"
            }),
            j: "ab939cb2-17-" + i0 + "," + ("ab939cb2-15-" + i0),
            k: common_vendor.p({
              text: formatPrice(quotation),
              css: getPriceStyle(quotation)
            }),
            l: "ab939cb2-15-" + i0 + "," + ("ab939cb2-11-" + i0),
            m: "ab939cb2-11-" + i0 + "," + ("ab939cb2-9-" + i0),
            n: quotation.id,
            o: "ab939cb2-9-" + i0 + ",ab939cb2-8"
          };
        }),
        k: common_vendor.p({
          css: "flex: 1;"
        }),
        l: common_vendor.p({
          css: "text-align: right;"
        }),
        m: common_vendor.p({
          css: "display: flex; justify-content: space-between; align-items: center;"
        }),
        n: common_vendor.p({
          css: "border: 1rpx solid #e4e7ed; border-radius: 12rpx; padding: 24rpx; margin-bottom: 20rpx;"
        }),
        o: common_vendor.p({
          css: "background: rgba(255, 255, 255, 0.95); border-radius: 20rpx; padding: 40rpx; margin-bottom: 30rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);"
        }),
        p: common_vendor.p({
          text: miniProgramPath.value,
          css: "width: 160rpx; height: 160rpx; margin: 0 auto 20rpx;"
        }),
        q: common_vendor.p({
          text: "微信扫码查看更多报价",
          css: "font-size: 28rpx; color: #606266; font-weight: 500; display: block; margin-bottom: 10rpx;"
        }),
        r: common_vendor.p({
          text: "由 NewDianJia 提供技术支持",
          css: "font-size: 24rpx; color: #909399; display: block;"
        }),
        s: common_vendor.p({
          css: "background: rgba(255, 255, 255, 0.95); border-radius: 20rpx; padding: 40rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12); text-align: center;"
        }),
        t: common_vendor.sr(posterRef, "ab939cb2-1", {
          "k": "posterRef"
        }),
        v: common_vendor.o(handleDone),
        w: common_vendor.o(handleFail),
        x: common_vendor.p({
          css: "width: 750rpx; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); padding: 60rpx 40rpx;",
          pathType: "url",
          performance: true
        })
      }) : {}, {
        y: showPreview.value && previewImagePath.value
      }, showPreview.value && previewImagePath.value ? {
        z: common_vendor.o(closePreview),
        A: previewImagePath.value,
        B: common_vendor.o(onImageLoad),
        C: common_vendor.o(saveToAlbum),
        D: common_vendor.p({
          type: "primary",
          ["custom-style"]: "flex: 1; height: 88rpx; font-size: 32rpx; font-weight: 500; border-radius: 50rpx; margin: 0; padding: 0 32rpx; min-width: 0; box-sizing: border-box;"
        }),
        E: common_vendor.o(closePreview),
        F: common_vendor.p({
          type: "info",
          ["custom-style"]: "flex: 1; height: 88rpx; font-size: 32rpx; font-weight: 500; border-radius: 50rpx; margin: 0; padding: 0 32rpx; min-width: 0; box-sizing: border-box;"
        })
      } : {});
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ab939cb2"]]);
wx.createComponent(Component);
