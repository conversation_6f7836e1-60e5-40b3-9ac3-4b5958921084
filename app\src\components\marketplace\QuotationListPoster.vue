<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import type { IUserPublicProfile, IQuotationResponse } from '@/types'

defineOptions({
  name: 'QuotationListPoster'
})

// Props
interface Props {
  visible: boolean
  userProfile: IUserPublicProfile
  quotationList: IQuotationResponse[]
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', path: string): void
  (e: 'fail', error: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const posterRef = ref()
const generatedImagePath = ref('')
const showPreview = ref(false)
const previewImagePath = ref('')

// 计算属性
const isVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 获取显示的报价列表（前3条）
const displayQuotationList = computed(() => {
  return props.quotationList.slice(0, 3)
})

// 获取小程序码路径（当前用户主页）
const miniProgramPath = computed(() => {
  return `/pages/quotes/public-list?id=${props.userProfile?.id || ''}`
})

// 监听visible变化，开始生成海报
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.userProfile && props.quotationList.length > 0) {
    isLoading.value = true
    // 下一个tick生成海报
    setTimeout(() => {
      generatePoster()
    }, 100)
  }
})

// 生成海报
async function generatePoster() {
  // lime-painter 会自动开始绘制，不需要手动调用
}

// lime-painter 绘制完成回调
function handleDone(result: any) {
  console.log('lime-painter 绘制完成:', result)
  
  // 绘制完成后，获取图片路径
  if (posterRef.value) {
    posterRef.value.canvasToTempFilePathSync({
      fileType: 'jpg',
      quality: 0.9,
      success: (res: any) => {
        console.log('海报生成成功:', res.tempFilePath)
        handleSuccess(res.tempFilePath)
      },
      fail: (error: any) => {
        console.error('海报生成失败:', error)
        handleFail(error)
      }
    })
  }
}

// 海报生成成功
function handleSuccess(imagePath: string) {
  isLoading.value = false
  generatedImagePath.value = imagePath
  previewImagePath.value = imagePath
  emit('success', imagePath)
  
  // 显示自定义预览界面
  showPreview.value = true
}

// 海报生成失败
function handleFail(error: any) {
  isLoading.value = false
  console.error('海报生成失败:', error)
  emit('fail', error)
  
  uni.showToast({
    title: '海报生成失败',
    icon: 'error'
  })
}

// 检查相册权限
async function checkPhotoAlbumPermission(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        const authSetting = res.authSetting
        if (authSetting['scope.writePhotosAlbum'] === undefined) {
          // 未询问过权限，返回 true，让系统询问
          resolve(true)
        } else if (authSetting['scope.writePhotosAlbum'] === true) {
          // 已授权
          resolve(true)
        } else {
          // 已拒绝授权
          resolve(false)
        }
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// 请求相册权限
async function requestPhotoAlbumPermission(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.authorize({
      scope: 'scope.writePhotosAlbum',
      success: () => {
        resolve(true)
      },
      fail: () => {
        // 权限被拒绝，引导用户到设置页面
        uni.showModal({
          title: '需要相册权限',
          content: '保存图片需要访问您的相册，请在设置中开启相册权限',
          showCancel: true,
          confirmText: '去设置',
          success: (modalRes) => {
            if (modalRes.confirm) {
              uni.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.writePhotosAlbum']) {
                    resolve(true)
                  } else {
                    resolve(false)
                  }
                },
                fail: () => {
                  resolve(false)
                }
              })
            } else {
              resolve(false)
            }
          }
        })
      }
    })
  })
}

// 保存到相册
async function saveToAlbum() {
  if (!previewImagePath.value) {
    uni.showToast({
      title: '请先生成海报',
      icon: 'none'
    })
    return
  }
  
  try {
    // 检查权限
    const hasPermission = await checkPhotoAlbumPermission()
    
    let canSave = hasPermission
    if (!hasPermission) {
      // 请求权限
      canSave = await requestPhotoAlbumPermission()
    }
    
    if (!canSave) {
      uni.showToast({
        title: '需要相册权限才能保存',
        icon: 'none'
      })
      return
    }
    
    // 保存图片
    uni.saveImageToPhotosAlbum({
      filePath: previewImagePath.value,
      success: () => {
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      }
    })
  } catch (error) {
    console.error('保存过程出错:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  }
}

// 关闭预览和海报
function closePreview() {
  showPreview.value = false
  // 通过emit通知父组件关闭
  emit('update:visible', false)
  // 清理状态
  generatedImagePath.value = ''
  previewImagePath.value = ''
}

// 格式化价格显示
function formatPrice(quotation: IQuotationResponse): string {
  if (quotation.priceType === 'Fixed') {
    return formatLargeNumber(quotation.price)
  } else {
    const formatted = formatLargeNumber(Math.abs(quotation.price))
    return quotation.price >= 0 ? `+${formatted}` : `-${formatted}`
  }
}

// 格式化大数字
function formatLargeNumber(num: number): string {
  const absNum = Math.abs(num)
  
  if (absNum < 10000) {
    return absNum.toLocaleString()
  }
  
  if (absNum < 100000000) {
    const wan = absNum / 10000
    return wan >= 100 ? Math.round(wan) + '万' : wan.toFixed(1) + '万'
  }
  
  const yi = absNum / 100000000
  return yi >= 100 ? Math.round(yi) + '亿' : yi.toFixed(1) + '亿'
}

// 获取合约名称
function getContractName(quotation: IQuotationResponse): string {
  return quotation.instrumentRef?.instrument_id || "未指定合约"
}

// 获取价格样式
function getPriceStyle(quotation: IQuotationResponse): string {
  const baseStyle = 'font-size: 32rpx; font-weight: 900; display: block;'
  
  if (quotation.priceType === 'Fixed') {
    return `${baseStyle} color: #1890ff;`
  } else {
    const color = quotation.price >= 0 ? '#ff4d4f' : '#52c41a'
    return `${baseStyle} color: ${color};`
  }
}

// 图片加载完成
function onImageLoad() {
  console.log('预览图片加载完成')
}
</script>

<template>
  <!-- 海报生成组件容器 -->
  <view v-if="isVisible" class="poster-generator">
    <!-- 加载提示（固定在屏幕中央） -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-content">
        <wd-loading size="48rpx" />
        <text class="loading-text">正在生成海报...</text>
      </view>
    </view>
    
    <!-- 海报生成组件（隐藏在屏幕外） -->
    <view class="poster-content">
      <!-- Lime Painter 组件 -->
      <l-painter
        ref="posterRef"
        css="width: 750rpx; color: #1a1a1a; padding: 60rpx 40rpx; background: linear-gradient(,#ff971b 0%, #ff5000 100%)"
        @done="handleDone"
        @fail="handleFail"
        pathType="url"
        performance
        style="position: fixed; left: -2000rpx; top: -2000rpx;"
      >
        <!-- 顶部 - 用户信息区域 -->
        <l-painter-view css="border-radius: 20rpx; padding: 40rpx; margin-bottom: 30rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);">
          <!-- 用户头像 -->
          <l-painter-image
            :src="userProfile.headerImg || '/static/images/default-avatar.png'"
            css="width: 120rpx; height: 120rpx; border-radius: 50%; object-fit: cover; border: 4rpx solid rgba(102, 126, 234, 0.1); display: inline-block; vertical-align: top;"
          />

          <!-- 用户信息 -->
          <l-painter-view css="display: inline-block; vertical-align: top; margin-left: 30rpx;">
            <!-- 公司名称 -->
            <l-painter-text
              :text="userProfile.companyName || '未指定公司名称'"
              css="font-size: 36rpx; font-weight: bold; color: #1a1a1a; line-height: 50rpx; display: block; margin-bottom: 16rpx;"
            />

            <!-- 联系人 nickName -->
            <l-painter-text
              :text="userProfile.nickName ? `联系人：${userProfile.nickName}` : ''"
              css="font-size: 28rpx; color: #666; line-height: 40rpx; display: block; margin-bottom: 12rpx;"
            />

            <!-- 手机号码 - 注意：当前接口中没有手机号字段 -->
            <l-painter-text
              text="手机：***-****-****"
              css="font-size: 26rpx; color: #909399; line-height: 36rpx; display: block;"
            />
          </l-painter-view>
        </l-painter-view>
        
        <!-- 中部 - 报价列表区域 -->
        <l-painter-view css="background: rgba(255, 255, 255, 0.95); border-radius: 20rpx; padding: 40rpx; margin-bottom: 30rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);">
          <!-- 报价列表 -->
          <l-painter-view
            v-for="quotation in displayQuotationList"
            :key="quotation.id"
            css="border: 1rpx solid #e4e7ed; border-radius: 12rpx; padding: 4rpx; margin-bottom: 20rpx;"
          >
            <!-- 报价标题 -->
            <l-painter-text
              :text="quotation.title"
              css="font-size: 28rpx; font-weight: normal; color: #303133; line-height: 40rpx; display: block; margin-bottom: 12rpx;"
            />

            <!-- 商品信息 -->
            <l-painter-text
              :text="quotation.commodityName || ''"
              css="font-size: 26rpx; color: #606266; display: block; background: #f0f9ff; padding: 4rpx 12rpx; border-radius: 8rpx; margin-bottom: 8rpx;"
            />
            <l-painter-text
              :text="quotation.deliveryLocation || ''"
              css="font-size: 26rpx; color: #606266; display: block; background: #f0f9f4; padding: 4rpx 12rpx; border-radius: 8rpx; margin-bottom: 8rpx;"
            />

            <!-- 价格信息 -->
            <l-painter-text
              :text="quotation.priceType === 'Basis' ? getContractName(quotation) : ''"
              css="font-size: 24rpx; color: #595959; font-weight: bold; display: block; margin-bottom: 4rpx;"
            />
            <l-painter-text
              :text="formatPrice(quotation)"
              :css="getPriceStyle(quotation)"
            />
          </l-painter-view>
        </l-painter-view>
        
        <!-- 底部 - 扫码区域 -->
        <l-painter-view css="background: rgba(255, 255, 255, 0.95); border-radius: 20rpx; padding: 40rpx; box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12); text-align: center;">
          <!-- 二维码 -->
          <l-painter-qrcode
            :text="miniProgramPath"
            css="width: 160rpx; height: 160rpx; margin-bottom: 20rpx;"
          />

          <!-- 引导文案 -->
          <l-painter-text
            text="微信扫码查看更多报价"
            css="font-size: 28rpx; color: #606266; font-weight: normal; display: block; margin-bottom: 10rpx;"
          />

          <!-- 技术支持 -->
          <l-painter-text
            text="由 NewDianJia 提供技术支持"
            css="font-size: 24rpx; color: #909399; display: block;"
          />
        </l-painter-view>
      </l-painter>
    </view>
  </view>
  
  <!-- 图片预览弹窗 -->
  <view v-if="showPreview && previewImagePath" class="preview-popup">
    <!-- 遮罩层 -->
    <view class="preview-overlay" @click="closePreview"></view>
    
    <!-- 预览内容 -->
    <view class="preview-wrapper">
      <!-- 图片容器 -->
      <scroll-view 
        scroll-y 
        class="preview-scroll"
        enhanced
        :show-scrollbar="false"
      >
        <view class="preview-image-container">
          <image 
            :src="previewImagePath" 
            mode="widthFix" 
            class="preview-full-image"
            @load="onImageLoad"
          />
        </view>
      </scroll-view>
      
      <!-- 底部操作栏 -->
      <view class="preview-actions">
        <wd-button
          type="primary"
          @click="saveToAlbum"
          custom-style="flex: 1; height: 88rpx; font-size: 32rpx; font-weight: 500; border-radius: 50rpx; margin: 0; padding: 0 32rpx; min-width: 0; box-sizing: border-box;"
        >
          保存到相册
        </wd-button>
        <wd-button
          type="info"
          @click="closePreview"
          custom-style="flex: 1; height: 88rpx; font-size: 32rpx; font-weight: 500; border-radius: 50rpx; margin: 0; padding: 0 32rpx; min-width: 0; box-sizing: border-box;"
        >
          关闭
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.poster-generator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 60rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  
  .loading-text {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #606266;
    font-weight: 500;
  }
}

.poster-content {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
  visibility: hidden;
  opacity: 0;
  z-index: -1;
}


// 预览弹窗样式
.preview-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8rpx);
}

.preview-wrapper {
  position: relative;
  z-index: 10001;
  width: 90%;
  height: 90%;
  max-width: 800rpx;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.preview-scroll {
  flex: 1;
  overflow: hidden;
}

.preview-image-container {
  padding: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}

.preview-full-image {
  width: 100%;
  max-width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.preview-actions {
  display: flex;
  gap: 24rpx;
  width: 100%;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.98);
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
}
</style>
